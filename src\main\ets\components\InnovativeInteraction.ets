import gestureRecognizer from '@ohos.ai.gestureRecognizer';
import eyeTracker from '@ohos.ai.eyeTracker';
import motionSensor from '@ohos.sensor.motion';
import { Transaction, Category } from '../models/Transaction';
import { AISmartAgent } from '../services/AISmartAgent';
import hilog from '@ohos.hilog';

/**
 * 创新交互组件集合
 * 基于HarmonyOS 6.0的新交互能力
 */

/**
 * 手势记账组件
 * 支持空中手势快速记账
 */
@Component
export struct GestureAccounting {
  @State isGestureMode: boolean = false;
  @State recognizedGesture: string = '';
  @State currentAmount: number = 0;
  @State selectedCategory: Category | null = null;
  @State gestureHint: string = '请做手势开始记账';
  
  private gestureRecognizer: gestureRecognizer.GestureRecognizer | null = null;
  private aiAgent: AISmartAgent = AISmartAgent.getInstance();
  private gestureMapping: Map<string, GestureAction> = new Map();

  aboutToAppear() {
    this.initializeGestureRecognizer();
    this.setupGestureMapping();
  }

  build() {
    Column({ space: 16 }) {
      // 手势模式切换
      Row() {
        Text('手势记账')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
        
        Spacer()
        
        Toggle({ type: ToggleType.Switch, isOn: this.isGestureMode })
          .onChange((isOn: boolean) => {
            this.isGestureMode = isOn;
            if (isOn) {
              this.startGestureRecognition();
            } else {
              this.stopGestureRecognition();
            }
          })
      }
      .width('100%')

      if (this.isGestureMode) {
        // 手势识别区域
        Column({ space: 12 }) {
          // 手势提示
          Text(this.gestureHint)
            .fontSize(14)
            .fontColor('#666666')
            .textAlign(TextAlign.Center)
            .width('100%')

          // 手势识别状态
          if (this.recognizedGesture) {
            Row({ space: 8 }) {
              Image($r('app.media.ic_gesture'))
                .width(20)
                .height(20)
              
              Text(`识别到手势: ${this.recognizedGesture}`)
                .fontSize(14)
                .fontColor('#4CAF50')
            }
          }

          // 当前记账信息
          if (this.currentAmount > 0 || this.selectedCategory) {
            Column({ space: 8 }) {
              if (this.currentAmount > 0) {
                Text(`金额: ¥${this.currentAmount}`)
                  .fontSize(16)
                  .fontWeight(FontWeight.Medium)
              }
              
              if (this.selectedCategory) {
                Row({ space: 8 }) {
                  Text(this.selectedCategory.icon)
                    .fontSize(16)
                  
                  Text(this.selectedCategory.name)
                    .fontSize(14)
                }
              }
            }
            .padding(12)
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .width('100%')
          }

          // 手势指南
          this.buildGestureGuide();
        }
        .width('100%')
        .padding(16)
        .backgroundColor('#FAFAFA')
        .borderRadius(12)
      }
    }
    .width('100%')
    .padding(16)
  }

  @Builder
  buildGestureGuide() {
    Column({ space: 8 }) {
      Text('手势指南')
        .fontSize(14)
        .fontWeight(FontWeight.Medium)
      
      Grid() {
        GridItem() {
          this.buildGestureItem('👆', '向上滑动', '增加金额')
        }
        GridItem() {
          this.buildGestureItem('👇', '向下滑动', '减少金额')
        }
        GridItem() {
          this.buildGestureItem('👈', '向左滑动', '切换分类')
        }
        GridItem() {
          this.buildGestureItem('👉', '向右滑动', '确认记账')
        }
        GridItem() {
          this.buildGestureItem('✊', '握拳', '取消操作')
        }
        GridItem() {
          this.buildGestureItem('✋', '张开手掌', '重置输入')
        }
      }
      .columnsTemplate('1fr 1fr')
      .rowsGap(8)
      .columnsGap(8)
    }
  }

  @Builder
  buildGestureItem(icon: string, gesture: string, action: string) {
    Column({ space: 4 }) {
      Text(icon)
        .fontSize(20)
      
      Text(gesture)
        .fontSize(10)
        .fontColor('#666666')
      
      Text(action)
        .fontSize(10)
        .fontColor('#999999')
    }
    .padding(8)
    .backgroundColor(Color.White)
    .borderRadius(6)
    .width('100%')
  }

  private async initializeGestureRecognizer(): Promise<void> {
    try {
      this.gestureRecognizer = await gestureRecognizer.createGestureRecognizer({
        enableAirGesture: true,
        enableHandTracking: true,
        gestureTypes: ['swipe_up', 'swipe_down', 'swipe_left', 'swipe_right', 'fist', 'palm']
      });

      this.gestureRecognizer.on('gestureRecognized', (gesture) => {
        this.handleGestureRecognized(gesture);
      });
    } catch (error) {
      hilog.error(0x0013, 'GestureAccounting', 'Failed to initialize gesture recognizer: %{public}s', JSON.stringify(error));
    }
  }

  private setupGestureMapping(): void {
    this.gestureMapping.set('swipe_up', { type: 'amount', action: 'increase', value: 10 });
    this.gestureMapping.set('swipe_down', { type: 'amount', action: 'decrease', value: 10 });
    this.gestureMapping.set('swipe_left', { type: 'category', action: 'previous' });
    this.gestureMapping.set('swipe_right', { type: 'confirm', action: 'save' });
    this.gestureMapping.set('fist', { type: 'cancel', action: 'reset' });
    this.gestureMapping.set('palm', { type: 'reset', action: 'clear' });
  }

  private async startGestureRecognition(): Promise<void> {
    if (this.gestureRecognizer) {
      await this.gestureRecognizer.startRecognition();
      this.gestureHint = '请在摄像头前做手势';
    }
  }

  private async stopGestureRecognition(): Promise<void> {
    if (this.gestureRecognizer) {
      await this.gestureRecognizer.stopRecognition();
      this.gestureHint = '手势识别已停止';
    }
  }

  private async handleGestureRecognized(gesture: any): Promise<void> {
    this.recognizedGesture = gesture.type;
    const action = this.gestureMapping.get(gesture.type);
    
    if (!action) return;

    switch (action.type) {
      case 'amount':
        this.handleAmountGesture(action);
        break;
      case 'category':
        await this.handleCategoryGesture(action);
        break;
      case 'confirm':
        await this.handleConfirmGesture();
        break;
      case 'cancel':
      case 'reset':
        this.handleResetGesture();
        break;
    }
  }

  private handleAmountGesture(action: GestureAction): void {
    if (action.action === 'increase') {
      this.currentAmount += action.value || 10;
    } else if (action.action === 'decrease') {
      this.currentAmount = Math.max(0, this.currentAmount - (action.value || 10));
    }
    
    this.gestureHint = `当前金额: ¥${this.currentAmount}`;
  }

  private async handleCategoryGesture(action: GestureAction): Promise<void> {
    // 这里应该切换到下一个分类
    this.gestureHint = '切换分类';
  }

  private async handleConfirmGesture(): Promise<void> {
    if (this.currentAmount > 0 && this.selectedCategory) {
      // 创建交易记录
      const transaction: Partial<Transaction> = {
        type: 'expense',
        amount: this.currentAmount,
        categoryId: this.selectedCategory.id,
        categoryName: this.selectedCategory.name,
        description: '手势记账',
        date: new Date()
      };

      // 保存交易
      this.gestureHint = '记账成功！';
      this.handleResetGesture();
    } else {
      this.gestureHint = '请先设置金额和分类';
    }
  }

  private handleResetGesture(): void {
    this.currentAmount = 0;
    this.selectedCategory = null;
    this.recognizedGesture = '';
    this.gestureHint = '已重置，请重新开始';
  }
}

/**
 * 眼动记账组件
 * 支持眼动选择和确认
 */
@Component
export struct EyeTrackingAccounting {
  @State isEyeTrackingEnabled: boolean = false;
  @State gazePoint: { x: number, y: number } = { x: 0, y: 0 };
  @State selectedItem: string = '';
  @State dwellTime: number = 0;
  @State categories: Category[] = [];
  
  private eyeTracker: eyeTracker.EyeTracker | null = null;
  private dwellThreshold: number = 2000; // 2秒凝视确认

  aboutToAppear() {
    this.initializeEyeTracker();
    this.loadCategories();
  }

  build() {
    Column({ space: 16 }) {
      // 眼动追踪开关
      Row() {
        Text('眼动记账')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
        
        Spacer()
        
        Toggle({ type: ToggleType.Switch, isOn: this.isEyeTrackingEnabled })
          .onChange((isOn: boolean) => {
            this.isEyeTrackingEnabled = isOn;
            if (isOn) {
              this.startEyeTracking();
            } else {
              this.stopEyeTracking();
            }
          })
      }
      .width('100%')

      if (this.isEyeTrackingEnabled) {
        // 眼动状态指示
        Row({ space: 8 }) {
          Circle({ width: 8, height: 8 })
            .fill('#4CAF50')
          
          Text('眼动追踪已启用')
            .fontSize(12)
            .fontColor('#4CAF50')
        }

        // 分类选择区域
        Text('请用眼睛选择分类（凝视2秒确认）')
          .fontSize(14)
          .fontColor('#666666')
          .margin({ bottom: 8 })

        Grid() {
          ForEach(this.categories, (category: Category, index: number) => {
            GridItem() {
              this.buildEyeTrackingItem(category, index)
            }
          })
        }
        .columnsTemplate('1fr 1fr 1fr')
        .rowsGap(12)
        .columnsGap(12)

        // 凝视进度指示
        if (this.selectedItem && this.dwellTime > 0) {
          Column({ space: 8 }) {
            Text(`正在选择: ${this.selectedItem}`)
              .fontSize(14)
              .fontColor('#2196F3')
            
            Progress({
              value: this.dwellTime,
              total: this.dwellThreshold,
              type: ProgressType.Linear
            })
              .width('100%')
              .color('#2196F3')
          }
          .padding(12)
          .backgroundColor('#E3F2FD')
          .borderRadius(8)
        }
      }
    }
    .width('100%')
    .padding(16)
  }

  @Builder
  buildEyeTrackingItem(category: Category, index: number) {
    Column({ space: 8 }) {
      Text(category.icon)
        .fontSize(24)
      
      Text(category.name)
        .fontSize(12)
        .fontColor('#333333')
    }
    .width('100%')
    .height(80)
    .padding(8)
    .backgroundColor(this.selectedItem === category.name ? '#E3F2FD' : Color.White)
    .borderRadius(8)
    .border({
      width: this.selectedItem === category.name ? 2 : 1,
      color: this.selectedItem === category.name ? '#2196F3' : '#E0E0E0'
    })
    .justifyContent(FlexAlign.Center)
    .id(`category_${index}`)
  }

  private async initializeEyeTracker(): Promise<void> {
    try {
      this.eyeTracker = await eyeTracker.createEyeTracker({
        enableGazeTracking: true,
        enableBlinkDetection: true,
        calibrationRequired: true
      });

      this.eyeTracker.on('gazePoint', (point) => {
        this.handleGazePoint(point);
      });

      this.eyeTracker.on('blink', () => {
        this.handleBlink();
      });
    } catch (error) {
      hilog.error(0x0014, 'EyeTrackingAccounting', 'Failed to initialize eye tracker: %{public}s', JSON.stringify(error));
    }
  }

  private async startEyeTracking(): Promise<void> {
    if (this.eyeTracker) {
      await this.eyeTracker.startTracking();
    }
  }

  private async stopEyeTracking(): Promise<void> {
    if (this.eyeTracker) {
      await this.eyeTracker.stopTracking();
    }
  }

  private handleGazePoint(point: { x: number, y: number }): void {
    this.gazePoint = point;
    
    // 检测凝视的UI元素
    const hoveredItem = this.detectHoveredItem(point);
    
    if (hoveredItem !== this.selectedItem) {
      this.selectedItem = hoveredItem;
      this.dwellTime = 0;
    } else if (hoveredItem) {
      this.dwellTime += 100; // 假设每100ms更新一次
      
      if (this.dwellTime >= this.dwellThreshold) {
        this.confirmSelection(hoveredItem);
      }
    }
  }

  private handleBlink(): void {
    // 眨眼可以用作确认操作
    if (this.selectedItem && this.dwellTime > 1000) {
      this.confirmSelection(this.selectedItem);
    }
  }

  private detectHoveredItem(point: { x: number, y: number }): string {
    // 这里应该实现真正的UI元素检测逻辑
    // 简化实现：根据坐标范围判断
    return '';
  }

  private confirmSelection(item: string): void {
    // 确认选择
    this.dwellTime = 0;
    this.selectedItem = '';
    
    // 处理选择结果
    hilog.info(0x0014, 'EyeTrackingAccounting', 'Item selected by eye tracking: %{public}s', item);
  }

  private loadCategories(): void {
    // 加载分类数据
    this.categories = [
      { id: '1', name: '餐饮', icon: '🍔', type: 'expense', color: '#FF5722', isDefault: true, parentId: undefined, sortOrder: 1, isHidden: false, createdAt: new Date(), updatedAt: new Date() },
      { id: '2', name: '交通', icon: '🚗', type: 'expense', color: '#2196F3', isDefault: true, parentId: undefined, sortOrder: 2, isHidden: false, createdAt: new Date(), updatedAt: new Date() },
      { id: '3', name: '购物', icon: '🛍️', type: 'expense', color: '#9C27B0', isDefault: true, parentId: undefined, sortOrder: 3, isHidden: false, createdAt: new Date(), updatedAt: new Date() },
      { id: '4', name: '娱乐', icon: '🎮', type: 'expense', color: '#FF9800', isDefault: true, parentId: undefined, sortOrder: 4, isHidden: false, createdAt: new Date(), updatedAt: new Date() },
      { id: '5', name: '医疗', icon: '💊', type: 'expense', color: '#4CAF50', isDefault: true, parentId: undefined, sortOrder: 5, isHidden: false, createdAt: new Date(), updatedAt: new Date() },
      { id: '6', name: '教育', icon: '📚', type: 'expense', color: '#607D8B', isDefault: true, parentId: undefined, sortOrder: 6, isHidden: false, createdAt: new Date(), updatedAt: new Date() }
    ];
  }
}

/**
 * 智能预测输入组件
 * 基于AI的智能输入预测
 */
@Component
export struct SmartPredictiveInput {
  @State currentInput: string = '';
  @State predictions: InputPrediction[] = [];
  @State isLoading: boolean = false;
  @State selectedPrediction: InputPrediction | null = null;
  
  private aiAgent: AISmartAgent = AISmartAgent.getInstance();
  private debounceTimer: number = 0;

  build() {
    Column({ space: 12 }) {
      Text('智能预测输入')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)

      // 输入框
      TextInput({ placeholder: '输入消费描述...' })
        .onChange((value: string) => {
          this.currentInput = value;
          this.debouncedPredict(value);
        })
        .width('100%')

      // 预测结果
      if (this.isLoading) {
        Row({ space: 8 }) {
          LoadingProgress()
            .width(16)
            .height(16)
          
          Text('AI分析中...')
            .fontSize(12)
            .fontColor('#666666')
        }
      }

      if (this.predictions.length > 0) {
        Column({ space: 8 }) {
          Text('智能建议')
            .fontSize(14)
            .fontWeight(FontWeight.Medium)

          ForEach(this.predictions, (prediction: InputPrediction) => {
            this.buildPredictionItem(prediction);
          })
        }
        .width('100%')
      }
    }
    .width('100%')
    .padding(16)
  }

  @Builder
  buildPredictionItem(prediction: InputPrediction) {
    Row({ space: 12 }) {
      Column({ space: 4 }) {
        Text(`¥${prediction.amount}`)
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .fontColor('#4CAF50')
        
        Text(`${prediction.confidence}% 置信度`)
          .fontSize(10)
          .fontColor('#999999')
      }

      Column({ space: 4 }) {
        Row({ space: 4 }) {
          Text(prediction.category.icon)
            .fontSize(16)
          
          Text(prediction.category.name)
            .fontSize(14)
        }
        
        Text(prediction.account.name)
          .fontSize(12)
          .fontColor('#666666')
      }
      .layoutWeight(1)

      Button('选择')
        .type(ButtonType.Normal)
        .backgroundColor('#2196F3')
        .fontSize(12)
        .onClick(() => {
          this.selectPrediction(prediction);
        })
    }
    .width('100%')
    .padding(12)
    .backgroundColor('#F5F5F5')
    .borderRadius(8)
  }

  private debouncedPredict(input: string): void {
    clearTimeout(this.debounceTimer);
    this.debounceTimer = setTimeout(() => {
      this.predictInput(input);
    }, 500);
  }

  private async predictInput(input: string): Promise<void> {
    if (input.length < 2) {
      this.predictions = [];
      return;
    }

    this.isLoading = true;
    
    try {
      // 使用AI预测
      const categories = await this.aiAgent.suggestCategory(input, 0, new Date());
      
      // 生成预测结果
      this.predictions = categories.slice(0, 3).map((category, index) => ({
        amount: this.predictAmount(input, category),
        category,
        account: this.predictAccount(input, category),
        confidence: Math.max(60, 90 - index * 10),
        description: input
      }));
    } catch (error) {
      hilog.error(0x0015, 'SmartPredictiveInput', 'Prediction failed: %{public}s', JSON.stringify(error));
    } finally {
      this.isLoading = false;
    }
  }

  private predictAmount(input: string, category: Category): number {
    // 基于历史数据和分类预测金额
    const baseAmounts = {
      '餐饮': 35,
      '交通': 15,
      '购物': 80,
      '娱乐': 50,
      '医疗': 120,
      '教育': 200
    };
    
    return baseAmounts[category.name] || 50;
  }

  private predictAccount(input: string, category: Category): any {
    // 预测最可能的支付账户
    return {
      id: 'default',
      name: '支付宝'
    };
  }

  private selectPrediction(prediction: InputPrediction): void {
    this.selectedPrediction = prediction;
    
    // 触发选择事件
    hilog.info(0x0015, 'SmartPredictiveInput', 'Prediction selected: %{public}s', JSON.stringify(prediction));
  }
}

// 接口定义
interface GestureAction {
  type: 'amount' | 'category' | 'confirm' | 'cancel' | 'reset';
  action: string;
  value?: number;
}

interface InputPrediction {
  amount: number;
  category: Category;
  account: any;
  confidence: number;
  description: string;
}

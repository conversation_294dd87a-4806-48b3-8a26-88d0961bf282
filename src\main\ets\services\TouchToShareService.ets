import nfcController from '@ohos.nfc.controller';
import nfcTag from '@ohos.nfc.tag';
import distributedHardware from '@ohos.distributedHardware';
import { Transaction, Account } from '../models/Transaction';
import { AISmartAgent } from './AISmartAgent';
import hilog from '@ohos.hilog';

/**
 * 碰一碰快速记账服务
 * 基于HarmonyOS 6.0的碰一碰功能实现设备间快速协同记账
 */
export class TouchToShareService {
  private static readonly TAG = 'TouchToShareService';
  private static readonly DOMAIN = 0x0011;
  private static instance: TouchToShareService;
  
  private nfcController: nfcController.NfcController | null = null;
  private aiAgent: AISmartAgent;
  private isNfcEnabled: boolean = false;
  private shareCallbacks: Set<(data: ShareData) => void> = new Set();
  private collaborationSessions: Map<string, CollaborationSession> = new Map();

  private constructor() {
    this.aiAgent = AISmartAgent.getInstance();
  }

  static getInstance(): TouchToShareService {
    if (!TouchToShareService.instance) {
      TouchToShareService.instance = new TouchToShareService();
    }
    return TouchToShareService.instance;
  }

  /**
   * 初始化碰一碰服务
   */
  async initialize(): Promise<void> {
    try {
      await this.initNfcController();
      await this.checkNfcAvailability();
      await this.setupNfcListeners();
      
      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Touch to share service initialized successfully');
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Failed to initialize touch to share service: %{public}s', JSON.stringify(error));
      throw error;
    }
  }

  /**
   * 分享单个交易记录
   */
  async shareTransaction(transaction: Transaction, targetDeviceId?: string): Promise<void> {
    try {
      if (!this.isNfcEnabled) {
        throw new Error('NFC is not enabled');
      }

      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Starting transaction share: %{public}s', transaction.id);

      const shareData: ShareData = {
        type: 'transaction',
        data: transaction,
        sourceDeviceId: await this.getCurrentDeviceId(),
        timestamp: new Date(),
        shareId: this.generateShareId()
      };

      // 加密数据
      const encryptedData = await this.encryptShareData(shareData);

      if (targetDeviceId) {
        // 直接发送到指定设备
        await this.sendToDevice(encryptedData, targetDeviceId);
      } else {
        // 等待碰一碰
        await this.waitForTouch(encryptedData);
      }

      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Transaction shared successfully');
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Failed to share transaction: %{public}s', JSON.stringify(error));
      throw error;
    }
  }

  /**
   * 协同记账 - 多人聚餐分摊
   */
  async startCollaborativeAccounting(totalAmount: number, description: string, participants: string[]): Promise<string> {
    try {
      const sessionId = this.generateSessionId();
      const session: CollaborationSession = {
        id: sessionId,
        type: 'split_bill',
        totalAmount,
        description,
        participants,
        contributions: new Map(),
        status: 'active',
        createdAt: new Date(),
        createdBy: await this.getCurrentDeviceId()
      };

      this.collaborationSessions.set(sessionId, session);

      // 广播协同记账邀请
      await this.broadcastCollaborationInvite(session);

      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Collaborative accounting session started: %{public}s', sessionId);

      return sessionId;
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Failed to start collaborative accounting: %{public}s', JSON.stringify(error));
      throw error;
    }
  }

  /**
   * 加入协同记账
   */
  async joinCollaborativeAccounting(sessionId: string): Promise<void> {
    try {
      const session = this.collaborationSessions.get(sessionId);
      if (!session) {
        throw new Error('Collaboration session not found');
      }

      const deviceId = await this.getCurrentDeviceId();
      if (!session.participants.includes(deviceId)) {
        session.participants.push(deviceId);
      }

      // 通知其他参与者
      await this.notifyParticipants(session, 'participant_joined', { deviceId });

      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Joined collaborative accounting session: %{public}s', sessionId);
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Failed to join collaborative accounting: %{public}s', JSON.stringify(error));
      throw error;
    }
  }

  /**
   * 确认分摊金额
   */
  async confirmSplitAmount(sessionId: string, amount: number): Promise<void> {
    try {
      const session = this.collaborationSessions.get(sessionId);
      if (!session) {
        throw new Error('Collaboration session not found');
      }

      const deviceId = await this.getCurrentDeviceId();
      session.contributions.set(deviceId, amount);

      // 检查是否所有人都已确认
      const totalConfirmed = Array.from(session.contributions.values()).reduce((sum, amt) => sum + amt, 0);
      const allConfirmed = session.contributions.size === session.participants.length;

      if (allConfirmed) {
        if (Math.abs(totalConfirmed - session.totalAmount) < 0.01) {
          // 分摊完成，创建交易记录
          await this.finalizeSplitBill(session);
        } else {
          // 金额不匹配，需要调整
          await this.requestAmountAdjustment(session, totalConfirmed);
        }
      }

      // 通知其他参与者
      await this.notifyParticipants(session, 'amount_confirmed', { deviceId, amount });

      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Split amount confirmed: %{public}s, amount: %{public}f', sessionId, amount);
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Failed to confirm split amount: %{public}s', JSON.stringify(error));
      throw error;
    }
  }

  /**
   * 快速数据传输
   */
  async quickDataTransfer(data: any, targetDeviceId: string): Promise<void> {
    try {
      const transferData: TransferData = {
        type: 'quick_transfer',
        payload: data,
        sourceDeviceId: await this.getCurrentDeviceId(),
        targetDeviceId,
        timestamp: new Date(),
        transferId: this.generateTransferId()
      };

      // 使用高速传输通道
      await this.highSpeedTransfer(transferData);

      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Quick data transfer completed to device: %{public}s', targetDeviceId);
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Quick data transfer failed: %{public}s', JSON.stringify(error));
      throw error;
    }
  }

  /**
   * 接收分享数据
   */
  async receiveShareData(encryptedData: string): Promise<ShareData | null> {
    try {
      // 解密数据
      const shareData = await this.decryptShareData(encryptedData);
      
      if (!shareData) {
        hilog.warn(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
          'Failed to decrypt share data');
        return null;
      }

      // 验证数据完整性
      if (!await this.validateShareData(shareData)) {
        hilog.warn(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
          'Share data validation failed');
        return null;
      }

      // 处理不同类型的分享数据
      await this.processShareData(shareData);

      // 通知回调
      this.notifyShareCallbacks(shareData);

      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Share data received and processed: %{public}s', shareData.shareId);

      return shareData;
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Failed to receive share data: %{public}s', JSON.stringify(error));
      return null;
    }
  }

  /**
   * 添加分享回调
   */
  addShareCallback(callback: (data: ShareData) => void): void {
    this.shareCallbacks.add(callback);
  }

  /**
   * 移除分享回调
   */
  removeShareCallback(callback: (data: ShareData) => void): void {
    this.shareCallbacks.delete(callback);
  }

  /**
   * 获取活跃的协同会话
   */
  getActiveCollaborationSessions(): CollaborationSession[] {
    return Array.from(this.collaborationSessions.values())
      .filter(session => session.status === 'active');
  }

  // 私有方法实现
  private async initNfcController(): Promise<void> {
    try {
      this.nfcController = nfcController.getNfcController();
      
      if (!this.nfcController) {
        throw new Error('NFC controller not available');
      }
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Failed to initialize NFC controller: %{public}s', JSON.stringify(error));
      throw error;
    }
  }

  private async checkNfcAvailability(): Promise<void> {
    if (!this.nfcController) {
      throw new Error('NFC controller not initialized');
    }

    this.isNfcEnabled = await this.nfcController.isNfcAvailable() && 
                       await this.nfcController.isNfcOpen();

    if (!this.isNfcEnabled) {
      hilog.warn(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'NFC is not available or not enabled');
    }
  }

  private async setupNfcListeners(): Promise<void> {
    if (!this.nfcController) {
      return;
    }

    // 监听NFC标签发现
    this.nfcController.on('nfcStateChange', (state) => {
      this.isNfcEnabled = (state === nfcController.State.STATE_ON);
      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'NFC state changed: %{public}d', state);
    });

    // 监听设备碰触
    this.nfcController.on('tagDiscovered', async (tagInfo) => {
      await this.handleTagDiscovered(tagInfo);
    });
  }

  private async handleTagDiscovered(tagInfo: nfcTag.TagInfo): Promise<void> {
    try {
      hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'NFC tag discovered: %{public}s', tagInfo.uid);

      // 读取标签数据
      const tagData = await this.readTagData(tagInfo);
      
      if (tagData && tagData.type === 'qianji_share') {
        // 处理记账分享数据
        await this.receiveShareData(tagData.payload);
      }
    } catch (error) {
      hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
        'Failed to handle tag discovered: %{public}s', JSON.stringify(error));
    }
  }

  private async waitForTouch(encryptedData: string): Promise<void> {
    // 将数据写入NFC标签，等待其他设备碰触
    const tagData = {
      type: 'qianji_share',
      payload: encryptedData,
      timestamp: Date.now()
    };

    // 这里应该实现NFC标签写入逻辑
    hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
      'Waiting for device touch...');
  }

  private async sendToDevice(encryptedData: string, targetDeviceId: string): Promise<void> {
    // 直接发送到指定设备
    await distributedHardware.sendData(targetDeviceId, encryptedData);
  }

  private async encryptShareData(shareData: ShareData): Promise<string> {
    // 实现数据加密
    return JSON.stringify(shareData); // 简化实现
  }

  private async decryptShareData(encryptedData: string): Promise<ShareData | null> {
    try {
      return JSON.parse(encryptedData); // 简化实现
    } catch {
      return null;
    }
  }

  private async validateShareData(shareData: ShareData): Promise<boolean> {
    // 验证数据完整性和有效性
    return shareData.type && shareData.data && shareData.sourceDeviceId && shareData.timestamp;
  }

  private async processShareData(shareData: ShareData): Promise<void> {
    switch (shareData.type) {
      case 'transaction':
        await this.processTransactionShare(shareData.data as Transaction);
        break;
      case 'collaboration_invite':
        await this.processCollaborationInvite(shareData.data as CollaborationSession);
        break;
      case 'split_confirmation':
        await this.processSplitConfirmation(shareData.data);
        break;
      default:
        hilog.warn(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
          'Unknown share data type: %{public}s', shareData.type);
    }
  }

  private async processTransactionShare(transaction: Transaction): Promise<void> {
    // 处理接收到的交易记录
    hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
      'Processing shared transaction: %{public}s', transaction.id);
  }

  private async processCollaborationInvite(session: CollaborationSession): Promise<void> {
    // 处理协同记账邀请
    this.collaborationSessions.set(session.id, session);
    hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
      'Processing collaboration invite: %{public}s', session.id);
  }

  private async processSplitConfirmation(data: any): Promise<void> {
    // 处理分摊确认
    hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
      'Processing split confirmation');
  }

  private notifyShareCallbacks(shareData: ShareData): void {
    this.shareCallbacks.forEach(callback => {
      try {
        callback(shareData);
      } catch (error) {
        hilog.error(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
          'Error in share callback: %{public}s', JSON.stringify(error));
      }
    });
  }

  private async broadcastCollaborationInvite(session: CollaborationSession): Promise<void> {
    // 广播协同记账邀请
    const shareData: ShareData = {
      type: 'collaboration_invite',
      data: session,
      sourceDeviceId: await this.getCurrentDeviceId(),
      timestamp: new Date(),
      shareId: this.generateShareId()
    };

    // 通过NFC和其他方式广播
    await this.broadcastData(shareData);
  }

  private async notifyParticipants(session: CollaborationSession, eventType: string, eventData: any): Promise<void> {
    // 通知所有参与者
    for (const participantId of session.participants) {
      if (participantId !== await this.getCurrentDeviceId()) {
        await this.sendNotification(participantId, eventType, eventData);
      }
    }
  }

  private async finalizeSplitBill(session: CollaborationSession): Promise<void> {
    // 完成分摊，为每个参与者创建交易记录
    for (const [deviceId, amount] of session.contributions) {
      const transaction: Partial<Transaction> = {
        type: 'expense',
        amount,
        categoryId: 'dining',
        categoryName: '聚餐',
        description: `${session.description} (分摊)`,
        date: new Date(),
        isExcluded: false
      };

      await this.createTransactionForDevice(deviceId, transaction);
    }

    session.status = 'completed';
    hilog.info(TouchToShareService.DOMAIN, TouchToShareService.TAG, 
      'Split bill finalized: %{public}s', session.id);
  }

  private async requestAmountAdjustment(session: CollaborationSession, totalConfirmed: number): Promise<void> {
    // 请求金额调整
    const difference = session.totalAmount - totalConfirmed;
    await this.notifyParticipants(session, 'amount_adjustment_needed', { difference });
  }

  private async highSpeedTransfer(transferData: TransferData): Promise<void> {
    // 高速数据传输实现
    await distributedHardware.highSpeedTransfer(transferData.targetDeviceId, transferData);
  }

  private async readTagData(tagInfo: nfcTag.TagInfo): Promise<any> {
    // 读取NFC标签数据
    return null; // 简化实现
  }

  private async broadcastData(shareData: ShareData): Promise<void> {
    // 广播数据
  }

  private async sendNotification(deviceId: string, eventType: string, eventData: any): Promise<void> {
    // 发送通知
  }

  private async createTransactionForDevice(deviceId: string, transaction: Partial<Transaction>): Promise<void> {
    // 为指定设备创建交易记录
  }

  private async getCurrentDeviceId(): Promise<string> {
    return 'current_device_id'; // 简化实现
  }

  private generateShareId(): string {
    return `share_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateTransferId(): string {
    return `transfer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 接口定义
interface ShareData {
  type: 'transaction' | 'collaboration_invite' | 'split_confirmation' | 'quick_transfer';
  data: any;
  sourceDeviceId: string;
  timestamp: Date;
  shareId: string;
}

interface CollaborationSession {
  id: string;
  type: 'split_bill' | 'shared_expense';
  totalAmount: number;
  description: string;
  participants: string[];
  contributions: Map<string, number>;
  status: 'active' | 'completed' | 'cancelled';
  createdAt: Date;
  createdBy: string;
}

interface TransferData {
  type: string;
  payload: any;
  sourceDeviceId: string;
  targetDeviceId: string;
  timestamp: Date;
  transferId: string;
}

import UIAbility from '@ohos.app.ability.UIAbility';
import Want from '@ohos.app.ability.Want';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';
import hilog from '@ohos.hilog';
import { AISmartAgent } from '../services/AISmartAgent';
import { TouchToShareService } from '../services/TouchToShareService';
import { Transaction } from '../models/Transaction';

/**
 * 快速记账元服务
 * 轻量化的记账功能，支持快速启动和跨应用调用
 */
export default class QuickAccountingService extends UIAbility {
  private static readonly TAG = 'QuickAccountingService';
  private static readonly DOMAIN = 0x0012;
  
  private aiAgent: AISmartAgent;
  private touchToShare: TouchToShareService;

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
      'Quick Accounting Service onCreate');
    
    this.aiAgent = AISmartAgent.getInstance();
    this.touchToShare = TouchToShareService.getInstance();
    
    // 初始化服务
    this.initializeServices();
  }

  onNewWant(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
      'Quick Accounting Service onNewWant');
    
    // 处理新的调用请求
    this.handleNewRequest(want);
  }

  onDestroy(): void {
    hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
      'Quick Accounting Service onDestroy');
  }

  /**
   * 初始化服务
   */
  private async initializeServices(): Promise<void> {
    try {
      await this.aiAgent.initialize();
      await this.touchToShare.initialize();
      
      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Services initialized successfully');
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Failed to initialize services: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 处理新的调用请求
   */
  private async handleNewRequest(want: Want): Promise<void> {
    try {
      const action = want.action;
      const parameters = want.parameters;

      switch (action) {
        case 'quick.voice.recording':
          await this.handleVoiceRecording(parameters);
          break;
        case 'quick.image.recognition':
          await this.handleImageRecognition(parameters);
          break;
        case 'quick.share.transaction':
          await this.handleShareTransaction(parameters);
          break;
        case 'quick.split.bill':
          await this.handleSplitBill(parameters);
          break;
        case 'quick.budget.check':
          await this.handleBudgetCheck(parameters);
          break;
        default:
          hilog.warn(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
            'Unknown action: %{public}s', action);
      }
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Failed to handle request: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 处理语音记账
   */
  private async handleVoiceRecording(parameters: Record<string, Object>): Promise<void> {
    try {
      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Handling voice recording request');

      const transaction = await this.aiAgent.voiceRecording();
      
      if (transaction) {
        // 保存交易记录
        await this.saveTransaction(transaction);
        
        // 返回结果
        await this.returnResult('voice.recording.success', {
          transactionId: transaction.id,
          amount: transaction.amount,
          category: transaction.categoryName,
          description: transaction.description
        });
      } else {
        await this.returnResult('voice.recording.failed', {
          error: 'Failed to recognize voice input'
        });
      }
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Voice recording failed: %{public}s', JSON.stringify(error));
      
      await this.returnResult('voice.recording.error', {
        error: error.message
      });
    }
  }

  /**
   * 处理图像识别记账
   */
  private async handleImageRecognition(parameters: Record<string, Object>): Promise<void> {
    try {
      const imagePath = parameters['imagePath'] as string;
      
      if (!imagePath) {
        throw new Error('Image path is required');
      }

      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Handling image recognition request: %{public}s', imagePath);

      const transaction = await this.aiAgent.imageRecognition(imagePath);
      
      if (transaction) {
        // 保存交易记录
        await this.saveTransaction(transaction);
        
        // 返回结果
        await this.returnResult('image.recognition.success', {
          transactionId: transaction.id,
          amount: transaction.amount,
          category: transaction.categoryName,
          description: transaction.description,
          merchant: this.extractMerchantFromDescription(transaction.description)
        });
      } else {
        await this.returnResult('image.recognition.failed', {
          error: 'Failed to recognize image content'
        });
      }
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Image recognition failed: %{public}s', JSON.stringify(error));
      
      await this.returnResult('image.recognition.error', {
        error: error.message
      });
    }
  }

  /**
   * 处理分享交易
   */
  private async handleShareTransaction(parameters: Record<string, Object>): Promise<void> {
    try {
      const transactionId = parameters['transactionId'] as string;
      const targetDeviceId = parameters['targetDeviceId'] as string;
      
      if (!transactionId) {
        throw new Error('Transaction ID is required');
      }

      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Handling share transaction request: %{public}s', transactionId);

      // 获取交易记录
      const transaction = await this.getTransaction(transactionId);
      
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // 分享交易
      await this.touchToShare.shareTransaction(transaction, targetDeviceId);
      
      // 返回结果
      await this.returnResult('share.transaction.success', {
        transactionId,
        sharedTo: targetDeviceId || 'nearby_devices'
      });
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Share transaction failed: %{public}s', JSON.stringify(error));
      
      await this.returnResult('share.transaction.error', {
        error: error.message
      });
    }
  }

  /**
   * 处理分摊账单
   */
  private async handleSplitBill(parameters: Record<string, Object>): Promise<void> {
    try {
      const totalAmount = parameters['totalAmount'] as number;
      const description = parameters['description'] as string;
      const participants = parameters['participants'] as string[];
      
      if (!totalAmount || !description) {
        throw new Error('Total amount and description are required');
      }

      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Handling split bill request: %{public}f', totalAmount);

      // 启动协同记账
      const sessionId = await this.touchToShare.startCollaborativeAccounting(
        totalAmount, description, participants || []
      );
      
      // 返回结果
      await this.returnResult('split.bill.success', {
        sessionId,
        totalAmount,
        description,
        participantCount: participants?.length || 0
      });
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Split bill failed: %{public}s', JSON.stringify(error));
      
      await this.returnResult('split.bill.error', {
        error: error.message
      });
    }
  }

  /**
   * 处理预算检查
   */
  private async handleBudgetCheck(parameters: Record<string, Object>): Promise<void> {
    try {
      const userId = parameters['userId'] as string || 'default_user';
      
      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Handling budget check request for user: %{public}s', userId);

      // 检查预算状态
      const budgetAlerts = await this.aiAgent.budgetAlert(userId);
      
      // 获取消费分析
      const spendingInsight = await this.aiAgent.analyzeSpendingPattern(userId);
      
      // 返回结果
      await this.returnResult('budget.check.success', {
        alertCount: budgetAlerts.length,
        alerts: budgetAlerts.map(alert => ({
          category: alert.categoryName,
          percentage: alert.percentage,
          alertType: alert.alertType,
          message: alert.message
        })),
        insight: {
          confidence: spendingInsight.confidence,
          anomalyCount: spendingInsight.anomalies.length,
          recommendationCount: spendingInsight.recommendations.length
        }
      });
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Budget check failed: %{public}s', JSON.stringify(error));
      
      await this.returnResult('budget.check.error', {
        error: error.message
      });
    }
  }

  /**
   * 保存交易记录
   */
  private async saveTransaction(transaction: Transaction): Promise<void> {
    try {
      // 这里应该调用TransactionService保存交易
      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Transaction saved: %{public}s', transaction.id);
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Failed to save transaction: %{public}s', JSON.stringify(error));
      throw error;
    }
  }

  /**
   * 获取交易记录
   */
  private async getTransaction(transactionId: string): Promise<Transaction | null> {
    try {
      // 这里应该调用TransactionService获取交易
      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Getting transaction: %{public}s', transactionId);
      return null; // 简化实现
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Failed to get transaction: %{public}s', JSON.stringify(error));
      return null;
    }
  }

  /**
   * 返回结果给调用方
   */
  private async returnResult(resultCode: string, data: Record<string, any>): Promise<void> {
    try {
      const result = {
        resultCode,
        data,
        timestamp: new Date().toISOString()
      };

      // 通过Want返回结果
      const resultWant: Want = {
        action: 'quick.accounting.result',
        parameters: result
      };

      // 设置结果
      this.context.setResult(0, resultWant);
      
      hilog.info(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Result returned: %{public}s', resultCode);
    } catch (error) {
      hilog.error(QuickAccountingService.DOMAIN, QuickAccountingService.TAG, 
        'Failed to return result: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 从描述中提取商家信息
   */
  private extractMerchantFromDescription(description: string): string {
    // 简单的商家名称提取逻辑
    const merchants = ['星巴克', '麦当劳', '肯德基', '便利店', '超市', '餐厅'];
    
    for (const merchant of merchants) {
      if (description.includes(merchant)) {
        return merchant;
      }
    }
    
    return '未知商家';
  }
}

/**
 * 快速记账元服务工具类
 */
export class QuickAccountingUtils {
  /**
   * 调用语音记账
   */
  static async callVoiceRecording(): Promise<any> {
    const want: Want = {
      bundleName: 'com.qianji.harmonyos',
      abilityName: 'QuickAccountingService',
      action: 'quick.voice.recording'
    };

    return await globalThis.context.startAbility(want);
  }

  /**
   * 调用图像识别记账
   */
  static async callImageRecognition(imagePath: string): Promise<any> {
    const want: Want = {
      bundleName: 'com.qianji.harmonyos',
      abilityName: 'QuickAccountingService',
      action: 'quick.image.recognition',
      parameters: {
        imagePath
      }
    };

    return await globalThis.context.startAbility(want);
  }

  /**
   * 调用分享交易
   */
  static async callShareTransaction(transactionId: string, targetDeviceId?: string): Promise<any> {
    const want: Want = {
      bundleName: 'com.qianji.harmonyos',
      abilityName: 'QuickAccountingService',
      action: 'quick.share.transaction',
      parameters: {
        transactionId,
        targetDeviceId
      }
    };

    return await globalThis.context.startAbility(want);
  }

  /**
   * 调用分摊账单
   */
  static async callSplitBill(totalAmount: number, description: string, participants?: string[]): Promise<any> {
    const want: Want = {
      bundleName: 'com.qianji.harmonyos',
      abilityName: 'QuickAccountingService',
      action: 'quick.split.bill',
      parameters: {
        totalAmount,
        description,
        participants
      }
    };

    return await globalThis.context.startAbility(want);
  }

  /**
   * 调用预算检查
   */
  static async callBudgetCheck(userId?: string): Promise<any> {
    const want: Want = {
      bundleName: 'com.qianji.harmonyos',
      abilityName: 'QuickAccountingService',
      action: 'quick.budget.check',
      parameters: {
        userId
      }
    };

    return await globalThis.context.startAbility(want);
  }
}

/**
 * 快速记账卡片组件
 * 用于桌面小组件快速记账
 */
@Component
export struct QuickAccountingCard {
  @State isRecording: boolean = false;
  @State lastTransaction: string = '';

  build() {
    Column({ space: 12 }) {
      // 标题
      Text('快速记账')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .width('100%')
        .textAlign(TextAlign.Center)

      // 快速操作按钮
      Row({ space: 8 }) {
        Button('语音')
          .type(ButtonType.Normal)
          .backgroundColor('#4CAF50')
          .fontSize(12)
          .layoutWeight(1)
          .onClick(() => {
            this.startVoiceRecording();
          })

        Button('拍照')
          .type(ButtonType.Normal)
          .backgroundColor('#2196F3')
          .fontSize(12)
          .layoutWeight(1)
          .onClick(() => {
            this.startImageRecognition();
          })

        Button('分摊')
          .type(ButtonType.Normal)
          .backgroundColor('#FF9800')
          .fontSize(12)
          .layoutWeight(1)
          .onClick(() => {
            this.startSplitBill();
          })
      }

      // 最近记录
      if (this.lastTransaction) {
        Text(`最近: ${this.lastTransaction}`)
          .fontSize(10)
          .fontColor('#666666')
          .width('100%')
          .textAlign(TextAlign.Center)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
      }

      // 录音状态指示
      if (this.isRecording) {
        Row({ space: 4 }) {
          LoadingProgress()
            .width(12)
            .height(12)
            .color('#4CAF50')
          
          Text('正在录音...')
            .fontSize(10)
            .fontColor('#4CAF50')
        }
      }
    }
    .width('100%')
    .height('100%')
    .padding(12)
    .backgroundColor(Color.White)
    .borderRadius(8)
  }

  private async startVoiceRecording(): Promise<void> {
    try {
      this.isRecording = true;
      const result = await QuickAccountingUtils.callVoiceRecording();
      
      if (result.resultCode === 'voice.recording.success') {
        this.lastTransaction = `${result.data.category} ¥${result.data.amount}`;
      }
    } catch (error) {
      console.error('Voice recording failed:', error);
    } finally {
      this.isRecording = false;
    }
  }

  private async startImageRecognition(): Promise<void> {
    try {
      // 这里应该先选择图片，然后调用识别
      // const imagePath = await selectImage();
      // const result = await QuickAccountingUtils.callImageRecognition(imagePath);
      console.log('Image recognition started');
    } catch (error) {
      console.error('Image recognition failed:', error);
    }
  }

  private async startSplitBill(): Promise<void> {
    try {
      // 这里应该弹出分摊界面
      console.log('Split bill started');
    } catch (error) {
      console.error('Split bill failed:', error);
    }
  }
}
